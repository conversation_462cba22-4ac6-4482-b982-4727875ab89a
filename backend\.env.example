# Copy this file to .env and fill in your values

# Environment Mode
# Valid values: local, staging, production
ENV_MODE=local

#DATABASE
SUPABASE_URL=
SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_SSL=false

RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672

# LLM Providers:
ANTHROPIC_API_KEY=
OPENAI_API_KEY=
MODEL_TO_USE=

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION_NAME=

GROQ_API_KEY=
OPENROUTER_API_KEY=

# DATA APIS
RAPID_API_KEY=

# WEB SEARCH
TAVILY_API_KEY=

# WEB SCRAPE
FIRECRAWL_API_KEY=
FIRECRAWL_URL=

# Sandbox container provider:
DAYTONA_API_KEY=
DAYTONA_SERVER_URL=
DAYTONA_TARGET=

LANGFUSE_PUBLIC_KEY="pk-REDACTED"
LANGFUSE_SECRET_KEY="sk-REDACTED"
LANGFUSE_HOST="https://cloud.langfuse.com"

SMITHERY_API_KEY=

MCP_CREDENTIAL_ENCRYPTION_KEY=

QSTASH_URL="https://qstash.upstash.io"
QSTASH_TOKEN=""
QSTASH_CURRENT_SIGNING_KEY=""
QSTASH_NEXT_SIGNING_KEY=""

WEBHOOK_BASE_URL=""

# Optional
SLACK_CLIENT_ID=""
SLACK_CLIENT_SECRET=""
SLACK_REDIRECT_URI=""


PIPEDREAM_CLIENT_ID=""
PIPEDREAM_CLIENT_SECRET=""
PIPEDREAM_ENVIRONMENT=""
PIPEDREAM_PROJECT_ID=""
