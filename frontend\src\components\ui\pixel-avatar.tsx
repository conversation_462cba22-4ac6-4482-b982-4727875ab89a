import React from 'react';

export const PIXEL_ART_DESIGNS = {
  robot: `<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
    <rect x="4" y="2" width="8" height="6" fill="currentColor"/>
    <rect x="3" y="3" width="1" height="4" fill="currentColor"/>
    <rect x="12" y="3" width="1" height="4" fill="currentColor"/>
    <rect x="5" y="4" width="2" height="2" fill="white"/>
    <rect x="9" y="4" width="2" height="2" fill="white"/>
    <rect x="6" y="5" width="1" height="1" fill="currentColor"/>
    <rect x="9" y="5" width="1" height="1" fill="currentColor"/>
    <rect x="2" y="9" width="12" height="4" fill="currentColor"/>
    <rect x="4" y="10" width="8" height="2" fill="white"/>
    <rect x="5" y="11" width="6" height="1" fill="currentColor"/>
  </svg>`,
  
  cat: `<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
    <rect x="6" y="2" width="4" height="6" fill="currentColor"/>
    <rect x="5" y="1" width="2" height="2" fill="currentColor"/>
    <rect x="9" y="1" width="2" height="2" fill="currentColor"/>
    <rect x="4" y="3" width="8" height="5" fill="currentColor"/>
    <rect x="6" y="4" width="1" height="1" fill="white"/>
    <rect x="9" y="4" width="1" height="1" fill="white"/>
    <rect x="7" y="6" width="2" height="1" fill="white"/>
    <rect x="6" y="7" width="4" height="1" fill="white"/>
  </svg>`,
  
  wizard: `<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
    <rect x="7" y="1" width="2" height="1" fill="currentColor"/>
    <rect x="6" y="2" width="4" height="1" fill="currentColor"/>
    <rect x="5" y="3" width="6" height="1" fill="currentColor"/>
    <rect x="4" y="4" width="8" height="4" fill="currentColor"/>
    <rect x="6" y="5" width="1" height="1" fill="white"/>
    <rect x="9" y="5" width="1" height="1" fill="white"/>
    <rect x="7" y="7" width="2" height="1" fill="white"/>
    <rect x="3" y="8" width="10" height="5" fill="currentColor"/>
    <rect x="5" y="10" width="6" height="2" fill="white"/>
  </svg>`,
  
  knight: `<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
    <rect x="5" y="2" width="6" height="6" fill="currentColor"/>
    <rect x="4" y="3" width="8" height="4" fill="currentColor"/>
    <rect x="6" y="4" width="1" height="2" fill="white"/>
    <rect x="9" y="4" width="1" height="2" fill="white"/>
    <rect x="3" y="8" width="10" height="5" fill="currentColor"/>
    <rect x="5" y="10" width="6" height="2" fill="white"/>
    <rect x="7" y="1" width="2" height="2" fill="currentColor"/>
  </svg>`,
  
  ninja: `<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
    <rect x="4" y="3" width="8" height="5" fill="currentColor"/>
    <rect x="6" y="4" width="1" height="1" fill="white"/>
    <rect x="9" y="4" width="1" height="1" fill="white"/>
    <rect x="5" y="6" width="6" height="1" fill="white"/>
    <rect x="3" y="8" width="10" height="5" fill="currentColor"/>
    <rect x="5" y="10" width="6" height="2" fill="white"/>
    <rect x="2" y="2" width="12" height="1" fill="currentColor"/>
  </svg>`,
  
  pirate: `<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
    <rect x="4" y="3" width="8" height="5" fill="currentColor"/>
    <rect x="6" y="4" width="1" height="1" fill="white"/>
    <rect x="8" y="4" width="3" height="1" fill="white"/>
    <rect x="7" y="6" width="2" height="1" fill="white"/>
    <rect x="3" y="8" width="10" height="5" fill="currentColor"/>
    <rect x="5" y="10" width="6" height="2" fill="white"/>
    <rect x="3" y="2" width="10" height="1" fill="currentColor"/>
  </svg>`,
  
  alien: `<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
    <rect x="5" y="2" width="6" height="6" fill="currentColor"/>
    <rect x="4" y="3" width="8" height="4" fill="currentColor"/>
    <rect x="3" y="4" width="10" height="2" fill="currentColor"/>
    <rect x="5" y="5" width="2" height="2" fill="white"/>
    <rect x="9" y="5" width="2" height="2" fill="white"/>
    <rect x="2" y="8" width="12" height="4" fill="currentColor"/>
    <rect x="4" y="10" width="8" height="2" fill="white"/>
  </svg>`,
  
  dragon: `<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
    <rect x="6" y="2" width="4" height="6" fill="currentColor"/>
    <rect x="5" y="3" width="6" height="4" fill="currentColor"/>
    <rect x="6" y="4" width="1" height="1" fill="white"/>
    <rect x="9" y="4" width="1" height="1" fill="white"/>
    <rect x="4" y="1" width="2" height="2" fill="currentColor"/>
    <rect x="10" y="1" width="2" height="2" fill="currentColor"/>
    <rect x="3" y="8" width="10" height="4" fill="currentColor"/>
    <rect x="5" y="10" width="6" height="2" fill="white"/>
  </svg>`,
  
  ghost: `<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
    <rect x="4" y="3" width="8" height="10" fill="currentColor"/>
    <rect x="5" y="2" width="6" height="1" fill="currentColor"/>
    <rect x="6" y="1" width="4" height="1" fill="currentColor"/>
    <rect x="6" y="5" width="1" height="2" fill="white"/>
    <rect x="9" y="5" width="1" height="2" fill="white"/>
    <rect x="4" y="13" width="2" height="1" fill="transparent"/>
    <rect x="7" y="13" width="2" height="1" fill="transparent"/>
    <rect x="10" y="13" width="2" height="1" fill="transparent"/>
  </svg>`,
  
  bear: `<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
    <rect x="3" y="2" width="2" height="2" fill="currentColor"/>
    <rect x="11" y="2" width="2" height="2" fill="currentColor"/>
    <rect x="4" y="3" width="8" height="6" fill="currentColor"/>
    <rect x="6" y="5" width="1" height="1" fill="white"/>
    <rect x="9" y="5" width="1" height="1" fill="white"/>
    <rect x="7" y="7" width="2" height="1" fill="white"/>
    <rect x="3" y="9" width="10" height="4" fill="currentColor"/>
    <rect x="5" y="11" width="6" height="2" fill="white"/>
  </svg>`,
  
  astronaut: `<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
    <rect x="4" y="2" width="8" height="6" fill="currentColor"/>
    <rect x="3" y="3" width="10" height="4" fill="currentColor"/>
    <rect x="6" y="4" width="1" height="2" fill="white"/>
    <rect x="9" y="4" width="1" height="2" fill="white"/>
    <rect x="7" y="6" width="2" height="1" fill="white"/>
    <rect x="2" y="8" width="12" height="5" fill="currentColor"/>
    <rect x="4" y="10" width="8" height="2" fill="white"/>
    <rect x="1" y="9" width="2" height="2" fill="currentColor"/>
    <rect x="13" y="9" width="2" height="2" fill="currentColor"/>
  </svg>`,
  
  viking: `<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
    <rect x="4" y="3" width="8" height="5" fill="currentColor"/>
    <rect x="6" y="4" width="1" height="1" fill="white"/>
    <rect x="9" y="4" width="1" height="1" fill="white"/>
    <rect x="7" y="6" width="2" height="1" fill="white"/>
    <rect x="3" y="8" width="10" height="5" fill="currentColor"/>
    <rect x="5" y="10" width="6" height="2" fill="white"/>
    <rect x="2" y="1" width="3" height="3" fill="currentColor"/>
    <rect x="11" y="1" width="3" height="3" fill="currentColor"/>
    <rect x="7" y="0" width="2" height="2" fill="currentColor"/>
  </svg>`,
  
  demon: `<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
    <rect x="4" y="3" width="8" height="5" fill="currentColor"/>
    <rect x="5" y="4" width="2" height="2" fill="white"/>
    <rect x="9" y="4" width="2" height="2" fill="white"/>
    <rect x="6" y="5" width="1" height="1" fill="currentColor"/>
    <rect x="9" y="5" width="1" height="1" fill="currentColor"/>
    <rect x="7" y="7" width="2" height="1" fill="white"/>
    <rect x="3" y="8" width="10" height="5" fill="currentColor"/>
    <rect x="5" y="10" width="6" height="2" fill="white"/>
    <rect x="3" y="1" width="2" height="3" fill="currentColor"/>
    <rect x="11" y="1" width="2" height="3" fill="currentColor"/>
  </svg>`,
  
  samurai: `<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
    <rect x="4" y="3" width="8" height="5" fill="currentColor"/>
    <rect x="6" y="4" width="1" height="1" fill="white"/>
    <rect x="9" y="4" width="1" height="1" fill="white"/>
    <rect x="5" y="6" width="6" height="1" fill="white"/>
    <rect x="3" y="8" width="10" height="5" fill="currentColor"/>
    <rect x="5" y="10" width="6" height="2" fill="white"/>
    <rect x="6" y="1" width="4" height="2" fill="currentColor"/>
    <rect x="5" y="2" width="6" height="1" fill="currentColor"/>
  </svg>`,
  
  witch: `<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
    <rect x="4" y="3" width="8" height="5" fill="currentColor"/>
    <rect x="6" y="4" width="1" height="1" fill="white"/>
    <rect x="9" y="4" width="1" height="1" fill="white"/>
    <rect x="7" y="6" width="2" height="1" fill="white"/>
    <rect x="3" y="8" width="10" height="5" fill="currentColor"/>
    <rect x="5" y="10" width="6" height="2" fill="white"/>
    <rect x="6" y="0" width="4" height="3" fill="currentColor"/>
    <rect x="10" y="1" width="2" height="2" fill="currentColor"/>
  </svg>`,
  
  cyborg: `<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
    <rect x="4" y="2" width="8" height="6" fill="currentColor"/>
    <rect x="6" y="4" width="1" height="2" fill="white"/>
    <rect x="9" y="4" width="2" height="2" fill="white"/>
    <rect x="10" y="4" width="1" height="1" fill="currentColor"/>
    <rect x="7" y="6" width="2" height="1" fill="white"/>
    <rect x="3" y="8" width="10" height="5" fill="currentColor"/>
    <rect x="5" y="10" width="6" height="2" fill="white"/>
    <rect x="12" y="3" width="2" height="2" fill="currentColor"/>
  </svg>`
};

export const PIXEL_ART_CATEGORIES = {
  fantasy: {
    name: "Fantasy",
    designs: ["wizard", "knight", "dragon", "witch", "demon"]
  },
  scifi: {
    name: "Sci-Fi",
    designs: ["robot", "alien", "astronaut", "cyborg"]
  },
  animals: {
    name: "Animals",
    designs: ["cat", "bear", "ghost"]
  },
  warriors: {
    name: "Warriors",
    designs: ["ninja", "pirate", "viking", "samurai"]
  }
};

interface PixelAvatarProps {
  design: string;
  size?: number;
  className?: string;
  customPixels?: string;
}

export const PixelAvatar: React.FC<PixelAvatarProps> = ({ 
  design, 
  size = 32, 
  className = "",
  customPixels 
}) => {
  const pixelArt = customPixels || PIXEL_ART_DESIGNS[design as keyof typeof PIXEL_ART_DESIGNS];
  
  if (!pixelArt) {
    return (
      <div 
        className={`inline-block ${className}`} 
        style={{ width: size, height: size }}
      >
        <svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" className="w-full h-full">
          <rect x="4" y="2" width="8" height="6" fill="currentColor"/>
          <rect x="6" y="4" width="2" height="2" fill="white"/>
          <rect x="9" y="4" width="2" height="2" fill="white"/>
          <rect x="2" y="9" width="12" height="4" fill="currentColor"/>
        </svg>
      </div>
    );
  }

  return (
    <div 
      className={`inline-block ${className}`} 
      style={{ width: size, height: size }}
      dangerouslySetInnerHTML={{ __html: pixelArt }}
    />
  );
};

export const getAllPixelDesigns = (): string[] => {
  return Object.keys(PIXEL_ART_DESIGNS);
};

export const getPixelDesignsByCategory = (category: string): string[] => {
  return PIXEL_ART_CATEGORIES[category as keyof typeof PIXEL_ART_CATEGORIES]?.designs || [];
};

export const getRandomPixelDesign = (): string => {
  const designs = getAllPixelDesigns();
  return designs[Math.floor(Math.random() * designs.length)];
};

export const getPixelDesignFromSeed = (seed: string): string => {
  const designs = getAllPixelDesigns();
  let hash = 0;
  for (let i = 0; i < seed.length; i++) {
    const char = seed.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  const designIndex = Math.abs(hash) % designs.length;
  return designs[designIndex];
};