# Suna - 开源通用 AI 智能体详细介绍文档

## 项目概述

Suna 是一个功能强大的开源通用 AI 智能体平台，由 Kortix AI 开发。它通过自然语言对话为用户提供全面的数字助手服务，能够执行复杂的现实世界任务，包括研究、数据分析、网页自动化、文件管理等多种功能。

### 核心特性

- **🤖 智能对话界面**: 通过自然语言与 AI 助手进行交互
- **🌐 浏览器自动化**: 无缝的网页导航和数据提取
- **📁 文件管理**: 文档创建、编辑和处理
- **🔍 网络爬虫和搜索**: 扩展的搜索和数据收集能力
- **⚡ 命令行执行**: 系统任务和自动化脚本执行
- **🚀 网站部署**: 快速部署和托管功能
- **🔌 API 集成**: 与各种服务和 API 的集成
- **🔒 安全沙箱**: 隔离的执行环境确保安全性

## 项目结构

```
suna/
├── backend/                    # 后端服务
│   ├── agent/                 # 智能体核心
│   │   ├── tools/            # 工具集合
│   │   │   ├── sb_*.py       # 沙箱工具
│   │   │   ├── data_providers/ # 数据提供商
│   │   │   └── agent_builder_tools/ # 智能体构建工具
│   │   ├── run.py            # 智能体执行引擎
│   │   ├── prompt.py         # 系统提示词
│   │   └── workflows.py      # 工作流管理
│   ├── agentpress/           # 智能体框架
│   │   ├── thread_manager.py # 线程管理
│   │   ├── tool_registry.py  # 工具注册
│   │   └── response_processor.py # 响应处理
│   ├── sandbox/              # 沙箱环境
│   │   ├── sandbox.py        # 沙箱管理
│   │   ├── tool_base.py      # 工具基类
│   │   └── docker/           # Docker配置
│   ├── services/             # 核心服务
│   │   ├── supabase.py       # 数据库连接
│   │   ├── redis.py          # 缓存服务
│   │   ├── llm.py            # LLM集成
│   │   └── billing.py        # 计费系统
│   ├── mcp_service/          # MCP协议支持
│   ├── triggers/             # 触发器系统
│   ├── utils/                # 工具函数
│   ├── api.py                # 主API服务
│   └── pyproject.toml        # Python依赖
├── frontend/                  # 前端应用
│   ├── src/
│   │   ├── app/              # Next.js应用路由
│   │   ├── components/       # React组件
│   │   │   ├── dashboard/    # 仪表板组件
│   │   │   ├── thread/       # 对话组件
│   │   │   ├── agents/       # 智能体管理
│   │   │   └── ui/           # 基础UI组件
│   │   ├── hooks/            # React Hooks
│   │   ├── lib/              # 工具库
│   │   └── providers/        # 上下文提供者
│   ├── package.json          # Node.js依赖
│   └── next.config.ts        # Next.js配置
├── docs/                     # 文档
│   └── SELF-HOSTING.md       # 自托管指南
├── docker-compose.yaml       # Docker编排
├── setup.py                  # 安装向导
├── start.py                  # 启动脚本
└── README.md                 # 项目说明
```

## 技术架构

Suna 采用现代化的微服务架构，主要由四个核心组件构成：

### 1. 后端 API 服务 (Backend API)

- **技术栈**: Python + FastAPI
- **功能**:
  - REST API 端点管理
  - 线程和会话管理
  - LLM 集成（支持 Anthropic、OpenAI、Groq 等）
  - 用户认证和授权
  - 实时流式响应
- **核心文件**: `backend/api.py`, `backend/agent/run.py`

### 2. 前端用户界面 (Frontend)

- **技术栈**: Next.js 15 + React 18 + TypeScript
- **UI 框架**: Tailwind CSS + Radix UI
- **功能**:
  - 响应式聊天界面
  - 实时消息流
  - 文件上传和预览
  - 项目管理面板
  - 用户设置和配置
- **核心目录**: `frontend/src/`

### 3. 智能体 Docker 环境 (Agent Docker)

- **技术**: Docker 容器化 + Daytona SDK
- **功能**:
  - 隔离的执行环境
  - 浏览器自动化（Playwright）
  - 代码解释器
  - 文件系统访问
  - VNC 远程桌面
  - 安全特性和权限控制
- **核心目录**: `backend/sandbox/`

### 4. Supabase 数据库

- **技术**: PostgreSQL + Supabase
- **功能**:
  - 用户认证和管理
  - 对话历史存储
  - 文件存储和管理
  - 智能体状态管理
  - 实时订阅
  - 分析和监控数据

## 核心工具系统

Suna 的强大功能来源于其丰富的工具生态系统：

### 沙箱工具 (Sandbox Tools)

1. **SandboxShellTool**: 命令行执行和脚本运行
2. **SandboxFilesTool**: 文件操作和管理
3. **SandboxBrowserTool**: 网页自动化和数据提取
4. **SandboxVisionTool**: 图像识别和处理
5. **SandboxImageEditTool**: 图像编辑和生成
6. **SandboxDeployTool**: 应用部署和托管
7. **SandboxExposeTool**: 端口暴露和服务发布

### 数据提供商工具 (Data Providers)

- **LinkedinProvider**: LinkedIn 数据抓取
- **TwitterProvider**: Twitter/X 数据获取
- **AmazonProvider**: Amazon 产品信息
- **YahooFinanceProvider**: 金融数据获取
- **ZillowProvider**: 房地产数据
- **ActiveJobsProvider**: 招聘信息获取

### MCP 集成 (Model Context Protocol)

- **MCPToolWrapper**: MCP 协议工具包装器
- **SmitheryProvider**: Smithery 平台集成
- **PipedreamProvider**: Pipedream 工作流集成
- 支持自定义 MCP 服务器连接

## 工作流系统

Suna 支持复杂的工作流自动化：

### 工作流特性

- **可视化工作流编辑器**: 拖拽式流程设计
- **条件分支**: 基于条件的流程控制
- **循环执行**: 重复任务自动化
- **错误处理**: 异常情况处理机制
- **定时触发**: 基于时间的自动执行
- **Webhook 集成**: 外部事件触发

### 触发器系统

- **QStash 集成**: 后台任务处理
- **定时任务**: Cron 表达式支持
- **事件驱动**: 基于事件的自动化
- **API 触发**: RESTful API 调用触发

## 部署和配置

### 快速开始

```bash
# 克隆仓库
git clone https://github.com/kortix-ai/suna.git
cd suna

# 运行安装向导
python setup.py

# 启动服务
python start.py
```

### 环境要求

- **Docker**: 容器化部署
- **Python 3.11+**: 后端运行环境
- **Node.js 18+**: 前端构建环境
- **Supabase**: 数据库和认证
- **Redis**: 缓存和会话管理

### 必需的 API 密钥

- **LLM 提供商**: Anthropic/OpenAI/Groq 等
- **搜索服务**: Tavily/Firecrawl
- **执行环境**: Daytona
- **后台任务**: QStash
- **可选服务**: RapidAPI、Smithery

## 使用场景

### 1. 商业分析

- 竞争对手分析和市场研究
- 财务数据收集和报告生成
- 客户信息收集和 CRM 管理

### 2. 数据处理

- Excel/CSV 文件处理和分析
- 网页数据抓取和清洗
- 多源数据整合和报告

### 3. 内容创作

- 文档生成和编辑
- 网站开发和部署
- 多媒体内容处理

### 4. 自动化任务

- 社交媒体管理
- 邮件营销自动化
- 定期报告生成

### 5. 研究和学习

- 学术论文研究和总结
- 技术文档整理
- 知识库构建

## 实际使用示例

### 示例 1: 竞争对手分析

**用户输入**: "分析医疗保健行业在英国的市场情况，给我主要参与者、市场规模、优势和劣势，并添加他们的网站 URL。完成后生成 PDF 报告。"

**Suna 执行流程**:

1. 使用`SandboxWebSearchTool`搜索英国医疗保健市场信息
2. 使用`SandboxBrowserTool`访问相关网站收集数据
3. 使用`SandboxFilesTool`创建和编辑分析文档
4. 使用`SandboxShellTool`生成 PDF 报告
5. 通过`MessageTool`向用户展示结果

### 示例 2: LinkedIn 候选人搜索

**用户输入**: "在 LinkedIn 上找到 10 个可用的候选人档案 - 他们目前没有工作 - 适合初级软件工程师职位，位于德国慕尼黑。他们应该至少有计算机科学或相关领域的学士学位，以及任何领域/角色的 1 年经验。"

**Suna 执行流程**:

1. 使用`SandboxBrowserTool`自动化 LinkedIn 搜索
2. 使用`DataProvidersTool`中的`LinkedinProvider`提取候选人信息
3. 使用`SandboxVisionTool`分析候选人档案图片
4. 使用`SandboxFilesTool`整理候选人列表
5. 生成结构化的候选人报告

### 示例 3: Excel 数据处理

**用户输入**: "我的公司要求我建立一个 Excel 电子表格，包含意大利彩票游戏（Lotto、10eLotto 和 Million Day）的所有信息。基于此，生成并发送给我一个包含所有基本信息（公开信息）的电子表格。"

**Suna 执行流程**:

1. 使用`SandboxWebSearchTool`搜索意大利彩票信息
2. 使用`SandboxBrowserTool`访问官方彩票网站
3. 使用`SandboxShellTool`安装和运行 Excel 处理工具
4. 使用`SandboxFilesTool`创建和格式化 Excel 文件
5. 生成包含所有彩票信息的完整电子表格

## 配置和自定义

### 环境变量配置

```bash
# .env文件示例

# LLM提供商配置
ANTHROPIC_API_KEY=your_anthropic_key
OPENAI_API_KEY=your_openai_key
GROQ_API_KEY=your_groq_key

# 数据库配置
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# 搜索和爬虫
TAVILY_API_KEY=your_tavily_key
FIRECRAWL_API_KEY=your_firecrawl_key

# 沙箱执行
DAYTONA_API_KEY=your_daytona_key
DAYTONA_SERVER_URL=https://api.daytona.io

# 后台任务
QSTASH_TOKEN=your_qstash_token
QSTASH_CURRENT_SIGNING_KEY=your_signing_key

# 可选服务
RAPID_API_KEY=your_rapidapi_key
SMITHERY_API_KEY=your_smithery_key
```

### 自定义智能体配置

```json
{
  "name": "数据分析专家",
  "description": "专门用于数据分析和可视化的智能体",
  "system_prompt": "你是一个专业的数据分析师，擅长处理各种数据格式并生成洞察报告。",
  "enabled_tools": {
    "sb_shell_tool": { "enabled": true },
    "sb_files_tool": { "enabled": true },
    "sb_browser_tool": { "enabled": false },
    "web_search_tool": { "enabled": true },
    "data_providers_tool": { "enabled": true }
  },
  "model_config": {
    "model_name": "anthropic/claude-sonnet-4-20250514",
    "temperature": 0.1,
    "max_tokens": 4000
  }
}
```

### 工作流配置示例

```json
{
  "name": "市场研究工作流",
  "description": "自动化市场研究和报告生成",
  "trigger_phrase": "执行市场研究",
  "steps": [
    {
      "type": "search",
      "tool": "web_search_tool",
      "parameters": {
        "query": "{{industry}} market analysis {{region}}",
        "max_results": 10
      }
    },
    {
      "type": "browse",
      "tool": "sb_browser_tool",
      "parameters": {
        "urls": "{{search_results.urls}}",
        "extract_data": true
      }
    },
    {
      "type": "analyze",
      "tool": "sb_shell_tool",
      "parameters": {
        "command": "python analyze_market_data.py",
        "input_data": "{{browse_results}}"
      }
    },
    {
      "type": "report",
      "tool": "sb_files_tool",
      "parameters": {
        "action": "create",
        "filename": "market_report.pdf",
        "template": "market_analysis_template"
      }
    }
  ]
}
```

### Docker 部署配置

```yaml
# docker-compose.yaml核心配置
services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  rabbitmq:
    image: rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"

  backend:
    image: ghcr.io/suna-ai/suna-backend:latest
    ports:
      - "8000:8000"
    environment:
      - REDIS_HOST=redis
      - RABBITMQ_HOST=rabbitmq
    depends_on:
      - redis
      - rabbitmq

  worker:
    image: ghcr.io/suna-ai/suna-backend:latest
    command: uv run dramatiq --processes 4 --threads 4 run_agent_background
    depends_on:
      - redis
      - rabbitmq

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend
```

## 安全特性

### 沙箱隔离

- 每个智能体运行在独立的 Docker 容器中
- 文件系统隔离和权限控制
- 网络访问限制和监控

### 数据保护

- 端到端加密通信
- 敏感数据脱敏处理
- 访问日志和审计跟踪

### 用户认证

- JWT 令牌认证
- 多因素认证支持
- 角色和权限管理

## 开发和扩展

### 自定义工具开发

```python
from agentpress.tool import ToolResult, openapi_schema
from sandbox.tool_base import SandboxToolsBase

class CustomTool(SandboxToolsBase):
    @openapi_schema({
        "name": "custom_action",
        "description": "执行自定义操作",
        "parameters": {
            "type": "object",
            "properties": {
                "input": {"type": "string", "description": "输入参数"}
            }
        }
    })
    async def custom_action(self, input: str) -> ToolResult:
        # 自定义逻辑实现
        return self.success_response(f"处理结果: {input}")
```

### MCP 服务器集成

- 支持标准 MCP 协议
- 自定义服务器连接
- 工具动态加载和管理

## 监控和分析

### 性能监控

- **Langfuse**: LLM 调用跟踪和分析
- **Sentry**: 错误监控和报告
- **Prometheus**: 系统指标收集
- **Redis**: 实时状态监控

### 使用分析

- 用户行为分析
- 工具使用统计
- 性能优化建议
- 成本分析和优化

## 社区和支持

### 开源社区

- **GitHub**: https://github.com/kortix-ai/suna
- **Discord**: 社区讨论和支持
- **文档**: 详细的开发文档
- **贡献指南**: 参与开源贡献

### 商业支持

- 企业级部署支持
- 定制开发服务
- 技术咨询和培训
- SLA 保障服务

## 技术实现细节

### 智能体执行引擎

#### ThreadManager 核心机制

```python
class ThreadManager:
    def __init__(self, trace=None, is_agent_builder=False, target_agent_id=None, agent_config=None):
        self.tools = {}
        self.context_manager = ContextManager()
        self.response_processor = ResponseProcessor()
        self.trace = trace
        self.agent_config = agent_config
```

ThreadManager 负责：

- 工具注册和管理
- 上下文状态维护
- 响应流处理
- 错误处理和重试机制

#### 沙箱执行流程

1. **沙箱创建**: 使用 Daytona SDK 创建隔离容器
2. **环境初始化**: 安装必要的软件包和依赖
3. **会话管理**: 维护命令执行会话状态
4. **结果收集**: 捕获执行结果和输出
5. **资源清理**: 自动清理临时文件和进程

### 实时通信架构

#### WebSocket 流式响应

```typescript
const useAgentStream = (threadId: string) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);

  const streamResponse = async (input: string) => {
    const response = await fetch("/api/agent/stream", {
      method: "POST",
      body: JSON.stringify({ thread_id: threadId, message: input }),
      headers: { "Content-Type": "application/json" },
    });

    const reader = response.body?.getReader();
    // 处理流式数据...
  };
};
```

#### Redis 状态管理

- **会话状态**: 存储用户会话和上下文
- **任务队列**: 管理后台任务执行
- **缓存机制**: 提高响应速度
- **分布式锁**: 防止并发冲突

### 数据库设计

#### 核心表结构

```sql
-- 用户和账户
CREATE TABLE accounts (
    id UUID PRIMARY KEY,
    name TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 项目管理
CREATE TABLE projects (
    project_id UUID PRIMARY KEY,
    account_id UUID REFERENCES accounts(id),
    name TEXT NOT NULL,
    sandbox_id TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 对话线程
CREATE TABLE threads (
    thread_id UUID PRIMARY KEY,
    project_id UUID REFERENCES projects(project_id),
    title TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 消息存储
CREATE TABLE messages (
    id UUID PRIMARY KEY,
    thread_id UUID REFERENCES threads(thread_id),
    role TEXT NOT NULL, -- 'user' | 'assistant' | 'system'
    content JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 智能体配置
CREATE TABLE agents (
    agent_id UUID PRIMARY KEY,
    account_id UUID REFERENCES accounts(id),
    name TEXT NOT NULL,
    description TEXT,
    system_prompt TEXT,
    enabled_tools JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 工作流定义
CREATE TABLE agent_workflows (
    workflow_id UUID PRIMARY KEY,
    agent_id UUID REFERENCES agents(agent_id),
    name TEXT NOT NULL,
    description TEXT,
    trigger_phrase TEXT,
    steps JSONB NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    status TEXT DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 工具系统架构

#### 工具基类设计

```python
class SandboxToolsBase:
    def __init__(self, project_id: str, thread_manager: ThreadManager):
        self.project_id = project_id
        self.thread_manager = thread_manager
        self.sandbox = None

    async def get_sandbox(self):
        if not self.sandbox:
            self.sandbox = await get_or_create_sandbox(self.project_id)
        return self.sandbox

    def success_response(self, content: str, **kwargs) -> ToolResult:
        return ToolResult(success=True, content=content, **kwargs)

    def fail_response(self, error: str, **kwargs) -> ToolResult:
        return ToolResult(success=False, error=error, **kwargs)
```

#### 工具注册机制

```python
# 动态工具注册
def register_tools(thread_manager: ThreadManager, enabled_tools: dict):
    if enabled_tools.get('sb_shell_tool', {}).get('enabled', False):
        thread_manager.add_tool(SandboxShellTool,
                               project_id=project_id,
                               thread_manager=thread_manager)

    if enabled_tools.get('sb_browser_tool', {}).get('enabled', False):
        thread_manager.add_tool(SandboxBrowserTool,
                               project_id=project_id,
                               thread_id=thread_id,
                               thread_manager=thread_manager)
```

### MCP 协议集成

#### MCP 连接管理

```python
@dataclass
class MCPConnection:
    qualified_name: str
    name: str
    config: Dict[str, Any]
    enabled_tools: List[str]
    session: Optional[ClientSession] = None
    tools: Optional[List[Tool]] = None
    provider: Optional[str] = 'smithery'

class MCPManager:
    def __init__(self):
        self.connections: Dict[str, MCPConnection] = {}
        self._sessions: Dict[str, Tuple[Any, Any, Any]] = {}

    async def connect_to_server(self, connection: MCPConnection):
        # 建立MCP服务器连接
        # 获取可用工具列表
        # 注册工具到智能体
```

#### 工具动态加载

- **运行时发现**: 动态发现 MCP 服务器提供的工具
- **权限控制**: 基于用户权限过滤可用工具
- **配置管理**: 工具参数和凭据管理
- **错误处理**: 连接失败和重试机制

### 安全和权限控制

#### 沙箱安全机制

```dockerfile
# 沙箱Dockerfile安全配置
FROM ubuntu:22.04

# 创建非特权用户
RUN useradd -m -s /bin/bash sandbox_user
USER sandbox_user

# 限制网络访问
RUN iptables -A OUTPUT -d *************** -j DROP

# 文件系统权限
RUN chmod 755 /workspace
RUN chown sandbox_user:sandbox_user /workspace
```

#### API 安全

- **JWT 认证**: 基于令牌的身份验证
- **CORS 配置**: 跨域请求控制
- **速率限制**: 防止 API 滥用
- **输入验证**: 严格的参数验证

### 性能优化

#### 缓存策略

```python
# Redis缓存配置
CACHE_CONFIG = {
    'default_timeout': 3600,  # 1小时
    'key_prefix': 'suna:',
    'serializer': 'json'
}

# 智能缓存
@cache.memoize(timeout=3600)
async def get_agent_config(agent_id: str):
    # 缓存智能体配置
    pass

@cache.memoize(timeout=300)
async def get_tool_schema(tool_name: str):
    # 缓存工具模式
    pass
```

#### 异步处理

- **协程并发**: 使用 asyncio 提高并发性能
- **连接池**: 数据库和 HTTP 连接复用
- **后台任务**: Dramatiq 异步任务队列
- **流式处理**: 减少内存占用和延迟

### 监控和日志

#### 结构化日志

```python
import structlog

logger = structlog.get_logger()

# 上下文日志
structlog.contextvars.bind_contextvars(
    agent_run_id=agent_run_id,
    thread_id=thread_id,
    request_id=request_id,
)

logger.info("Agent execution started",
           model=model_name,
           tools_count=len(enabled_tools))
```

#### 指标收集

- **执行时间**: 工具调用和响应时间
- **错误率**: 失败请求统计
- **资源使用**: CPU、内存、存储监控
- **用户活跃度**: 使用模式分析

## 快速开始指南

### 1. 环境准备

```bash
# 确保已安装必要工具
docker --version
python --version  # 需要3.11+
node --version    # 需要18+
```

### 2. 获取 API 密钥

- 注册 Supabase 账户并创建项目
- 获取 Anthropic 或 OpenAI API 密钥
- 注册 Daytona 账户获取执行环境
- 获取 Tavily 搜索 API 密钥

### 3. 一键部署

```bash
# 克隆项目
git clone https://github.com/kortix-ai/suna.git
cd suna

# 运行安装向导
python setup.py

# 启动服务
python start.py
```

### 4. 访问应用

- 前端界面: http://localhost:3000
- 后端 API: http://localhost:8000
- API 文档: http://localhost:8000/docs

### 5. 第一次使用

1. 注册账户并登录
2. 创建新项目
3. 开始与 AI 助手对话
4. 尝试简单任务："帮我搜索今天的新闻"

## 项目优势

### 技术优势

- **🏗️ 模块化架构**: 易于扩展和维护
- **🔧 工具丰富**: 50+内置工具覆盖各种场景
- **🚀 高性能**: 异步处理和智能缓存
- **🔒 安全可靠**: 沙箱隔离和权限控制
- **📊 可观测性**: 完整的监控和日志系统

### 商业优势

- **💰 成本效益**: 开源免费，降低使用成本
- **🎯 定制化**: 支持自定义智能体和工作流
- **📈 可扩展**: 支持企业级部署和集群
- **🤝 社区支持**: 活跃的开源社区
- **🛡️ 数据安全**: 本地部署，数据完全可控

### 竞争优势

- **全功能集成**: 一个平台解决多种需求
- **真实执行**: 不仅是聊天，能真正执行任务
- **开源透明**: 代码开放，可审计和修改
- **生态丰富**: 支持 MCP 协议和第三方集成

## 发展路线图

### 短期目标 (3-6 个月)

- [ ] 增强移动端支持
- [ ] 添加更多数据源集成
- [ ] 优化性能和稳定性
- [ ] 扩展多语言支持

### 中期目标 (6-12 个月)

- [ ] 企业级功能增强
- [ ] 高级工作流编辑器
- [ ] 智能体市场平台
- [ ] 多模态能力扩展

### 长期目标 (1-2 年)

- [ ] 分布式执行架构
- [ ] 自主学习能力
- [ ] 行业特定解决方案
- [ ] 全球化部署支持

## 许可证

Suna 采用 Apache License 2.0 开源许可证，允许商业使用和修改。

## 联系方式

- **官网**: https://www.suna.so/
- **GitHub**: https://github.com/kortix-ai/suna
- **Discord**: https://discord.gg/Py6pCBUUPw
- **Twitter**: https://x.com/kortixai
- **邮箱**: <EMAIL>

---

_本文档基于 Suna 项目的最新版本编写，详细信息请参考官方文档和源代码。_

**最后更新**: 2025 年 1 月
**文档版本**: v1.0
**项目版本**: Suna v1.0
